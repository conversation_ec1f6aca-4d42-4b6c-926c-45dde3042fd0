0000000000000000000000000000000000000000 3dde78722f03ab0b6e8e2f124df12f2a389be855 frfi <<EMAIL>> 1745132424 +0200	commit (initial): Initialize repository
3dde78722f03ab0b6e8e2f124df12f2a389be855 f5d25329c70017ded951eef67c4742ea58c1582a frfi <<EMAIL>> 1745134673 +0200	commit: Add data importer module and graph data CSV for Neo4j
f5d25329c70017ded951eef67c4742ea58c1582a 6f337720cb6378ae1e05ea548f8dac0bdaab0d90 frfi <<EMAIL>> 1745136283 +0200	commit: Update Neo4j image version and refine entity linker to use fuzzy matching
6f337720cb6378ae1e05ea548f8dac0bdaab0d90 94981f5168946617a493d65679eb3661bf5eb698 frfi <<EMAIL>> 1745138300 +0200	commit: Refactor data importer and rename CSV files for consistency
94981f5168946617a493d65679eb3661bf5eb698 4ea64b73bf4c8d7fde9946eff01a363f3058c971 frfi <<EMAIL>> 1745139309 +0200	commit: Add delete option before data import and implement data deletion in Neo4j service
4ea64b73bf4c8d7fde9946eff01a363f3058c971 daf3b7d7d8cf13a1a4ffe17d0ff5730eae928c72 frfi <<EMAIL>> 1745140992 +0200	commit: Refactor data importer to improve directory handling and batch processing of nodes and relationships
daf3b7d7d8cf13a1a4ffe17d0ff5730eae928c72 ee6e8bff9b0c208d56b8c9f9a2f32e8e6ef91803 frfi <<EMAIL>> 1745143323 +0200	commit: Fix relationship import logic and update CSV relationship types for consistency
ee6e8bff9b0c208d56b8c9f9a2f32e8e6ef91803 827a363e80fda0bbe37ed34aebed50de69146ed0 frfi <<EMAIL>> 1745159326 +0200	commit: Implement FactExtractor class for atomic fact extraction from claims
827a363e80fda0bbe37ed34aebed50de69146ed0 4e2f798de83a6ea565cb9c552e46f619570bd8fd frfi <<EMAIL>> 1745223477 +0200	commit: Add fact extraction and verification examples, and refactor configuration files
4e2f798de83a6ea565cb9c552e46f619570bd8fd be2cb4c0ea2b6dc9a8d2489a67d5e859ec6f4ada frfi <<EMAIL>> 1745223507 +0200	commit: WIP
be2cb4c0ea2b6dc9a8d2489a67d5e859ec6f4ada c2eae6d8efdf8aaf84d314eaa6ca0b4990fc277a frfi <<EMAIL>> 1745224914 +0200	commit: Add gitignore
c2eae6d8efdf8aaf84d314eaa6ca0b4990fc277a fbe6601287d48290a797cfad8a600d08c9908084 frfi <<EMAIL>> 1745296680 +0200	commit: Add configuration files, setup script, and LLM service for fact extraction
fbe6601287d48290a797cfad8a600d08c9908084 d0f9cd5d5879d567e218269a2bdda528eac00e5a frfi <<EMAIL>> 1745296686 +0200	commit (amend): Add configuration files, setup script, and LLM service for fact extraction
d0f9cd5d5879d567e218269a2bdda528eac00e5a f18b084312585515d9437e3026542996ba2308f7 frfi <<EMAIL>> 1745298136 +0200	commit: Add .idea to .gitignore to exclude IDE-specific files from version control
f18b084312585515d9437e3026542996ba2308f7 90cb007bd759dc8ec7165656bec636e363726cb2 frfi <<EMAIL>> 1745302475 +0200	commit: WIP
90cb007bd759dc8ec7165656bec636e363726cb2 eedaf837e4056ac4103d02bf7615e35c70df0158 frfi <<EMAIL>> 1745765640 +0200	commit: Add configuration classes for knowledge graph, fact types, and verification process
eedaf837e4056ac4103d02bf7615e35c70df0158 f96e99031b5e443cae32e12293778c8cc0ccc8f4 frfi <<EMAIL>> 1745766801 +0200	commit: Refactor configuration handling for Neo4j connection and update README
f96e99031b5e443cae32e12293778c8cc0ccc8f4 b6ca29b7ce848f4fba8e3ec2c37e9d4a4b0fd397 frfi <<EMAIL>> 1745767880 +0200	commit: Enhance configuration management by adding knowledge graph connection and fuzzy retrieval settings; refactor logger initialization
b6ca29b7ce848f4fba8e3ec2c37e9d4a4b0fd397 438ff5c0429dc4540df01c2cc917496e4a7d7d6e frfi <<EMAIL>> 1745768020 +0200	commit: Add output type configuration to fact extraction settings
438ff5c0429dc4540df01c2cc917496e4a7d7d6e c4b086bb4cc38cc1e202622a9d19689003838d80 frfi <<EMAIL>> 1745817652 +0200	commit: Update .gitignore to include .idea directory and add test.yaml for question-answer pairs
c4b086bb4cc38cc1e202622a9d19689003838d80 7fd3675d7a044214d6433f118ed45011664b2e96 frfi <<EMAIL>> 1745819917 +0200	commit: Update test.yaml with detailed answers for various questions related to geological safety and engineering aspects of the deep geological repository.
7fd3675d7a044214d6433f118ed45011664b2e96 8221e524429ae7283bbb3864416f23be18efc15b frfi <<EMAIL>> 1745820235 +0200	commit: Refactor extractor module to improve type safety by introducing type definitions for extraction results and prompt input data
8221e524429ae7283bbb3864416f23be18efc15b ea11ccdc6899367b5dabd0f3f7113e6c49af2e10 frfi <<EMAIL>> 1745820889 +0200	commit: Add ruff linter
ea11ccdc6899367b5dabd0f3f7113e6c49af2e10 93e3ac960afe0bcab603ccc1fab66512da747bfc frfi <<EMAIL>> 1746077331 +0200	commit: Refactor extractor module to remove fallback extraction method and improve error handling
93e3ac960afe0bcab603ccc1fab66512da747bfc 1b71018f0a185f1c4ad54bda5e8a52288088f119 frfi <<EMAIL>> 1746177500 +0200	commit: Implement fuzzy retriever
1b71018f0a185f1c4ad54bda5e8a52288088f119 dbbae1f522434edfc26a19543d8005cf2a0733c2 frfi <<EMAIL>> 1746178129 +0200	commit (amend): Implement fuzzy retriever
dbbae1f522434edfc26a19543d8005cf2a0733c2 7c39c05c5e720b6de5f1fd965182e2e8ded95c02 frfi <<EMAIL>> 1746183886 +0200	commit: Fix template loader
7c39c05c5e720b6de5f1fd965182e2e8ded95c02 ba47f737d28b54e09d114c5066d7222c454b281a frfi <<EMAIL>> 1746203913 +0200	commit: Introduce Dbpedia as Knowledge Graph
ba47f737d28b54e09d114c5066d7222c454b281a 310d04643c637f921c2ac53da7cd32ed77ebd086 frfi <<EMAIL>> 1746206286 +0200	commit: Add Dbpedia as source
310d04643c637f921c2ac53da7cd32ed77ebd086 ee886c2b7212005a5f2725e538578c48849cdef6 frfi <<EMAIL>> 1746246261 +0200	commit: Create text cleaner
ee886c2b7212005a5f2725e538578c48849cdef6 d560691f30c664fc3db8d7d48d65453a25d39178 frfi <<EMAIL>> 1746246706 +0200	commit: Refactor relationship retrieval and update predicate labeling in Dbpedia service
d560691f30c664fc3db8d7d48d65453a25d39178 aa73be07691549ff4b16a1ca4761d12246510710 frfi <<EMAIL>> 1746247989 +0200	commit: Refactor Neo4j query handling and disable index creation
aa73be07691549ff4b16a1ca4761d12246510710 4879508f157358542e44d55c8a47b2ecc571b530 frfi <<EMAIL>> 1746261089 +0200	commit: Refactor fact extraction logic and update similarity threshold in settings
4879508f157358542e44d55c8a47b2ecc571b530 8937f67bda6d033b6c1829a48be327b2f8d0113a frfi <<EMAIL>> 1746340492 +0200	commit: Add mermaid graphs of human answers
8937f67bda6d033b6c1829a48be327b2f8d0113a 05cfd04a9012466f7826e9f87074b3d30f8bd91a frfi <<EMAIL>> 1746344688 +0200	commit (amend): Add mermaid graphs of human answers
05cfd04a9012466f7826e9f87074b3d30f8bd91a adf1390e461921a6898bf34857902f9155cd0dfa frfi <<EMAIL>> 1746348475 +0200	commit: Rename mermaid test files and update .gitignore to exclude IDE-specific files
adf1390e461921a6898bf34857902f9155cd0dfa 259eb886d1c667c9b0252c53a6fc14be334c4f1a frfi <<EMAIL>> 1746356490 +0200	commit: Update mermaid diagrams with additional questions and refine relationships
259eb886d1c667c9b0252c53a6fc14be334c4f1a e309ee3c52c04b17d4acff14111f57fa240a6986 frfi <<EMAIL>> 1746370889 +0200	commit: Update test.yaml with additional triples for long-term safety requirements
e309ee3c52c04b17d4acff14111f57fa240a6986 aaa34df7fc1f70282d21a901a3141ad6e1c6f7ee frfi <<EMAIL>> 1746417495 +0200	commit: Add test data loader module for parsing YAML test data
aaa34df7fc1f70282d21a901a3141ad6e1c6f7ee 0a1c058eca28945f75d7dc31bd8edf76d4f44772 frfi <<EMAIL>> 1746419951 +0200	commit: Refactor config imports and update settings for verifier configurations
0a1c058eca28945f75d7dc31bd8edf76d4f44772 4a2fd5706fdcb11b0e5166c7f4240c942b54a405 frfi <<EMAIL>> 1746420938 +0200	commit: Refactor configuration structure to include verifier settings and prompts
4a2fd5706fdcb11b0e5166c7f4240c942b54a405 cd6d5a545f9be3b637eea00600e66ca4989c74cc frfi <<EMAIL>> 1746422189 +0200	commit: Refactor prompt configurations to unify FactExtraction and Verifier prompts under a single PromptsConfig class
cd6d5a545f9be3b637eea00600e66ca4989c74cc b2e8ce07b422e8977cf6442c526766032e45de36 frfi <<EMAIL>> 1746424488 +0200	commit: Implement verifier configs
b2e8ce07b422e8977cf6442c526766032e45de36 82de8bf968c3060270ebe9652f5f30eaac9dde4a frfi <<EMAIL>> 1746507291 +0200	commit: Refactor extractor and verifier components to use new prompt data classes and improve verification logic
82de8bf968c3060270ebe9652f5f30eaac9dde4a 204634decc0abac22b42fb6895144e366e714116 frfi <<EMAIL>> 1746512542 +0200	commit: Refactor extractor and verifier components to use new prompt data classes and improve verification logic
204634decc0abac22b42fb6895144e366e714116 e010e74091c9ea0ffddef22d1c462eb3d333e46a frfi <<EMAIL>> 1746513509 +0200	commit: Create pipeline script
e010e74091c9ea0ffddef22d1c462eb3d333e46a 45cf8b601a8ed59b19d8a5c74048a630ff62f195 frfi <<EMAIL>> 1746591901 +0200	commit: Create pipeline script
45cf8b601a8ed59b19d8a5c74048a630ff62f195 f2199f5d8f7dbec1eb58f8259c51313a1f75f317 frfi <<EMAIL>> 1746594494 +0200	commit: Add dotenv support for environment variable loading and refactor config initialization
f2199f5d8f7dbec1eb58f8259c51313a1f75f317 ff8f16b66717bdb969ad347cb60c4989a6eac34a frfi <<EMAIL>> 1746596219 +0200	commit: Add evaluation functionality and Streamlit interface for fact verification
ff8f16b66717bdb969ad347cb60c4989a6eac34a c28b4e135f463a466aaf16bf47542c88601c7310 frfi <<EMAIL>> 1746596816 +0200	commit: Enhance evaluation display with metrics table and confusion matrix visualization
c28b4e135f463a466aaf16bf47542c88601c7310 fd0959f271199af0c18789837b301ada5ae76b19 frfi <<EMAIL>> 1747193976 +0200	commit: Add prompt request and result to extraction, enhance verification logging, and update Neo4j connection settings
fd0959f271199af0c18789837b301ada5ae76b19 ea2963f700f23b94e65268807b629623660dbae5 frfi <<EMAIL>> 1747194185 +0200	commit: Remove DBpedia service and related configurations from the project
ea2963f700f23b94e65268807b629623660dbae5 22e8ba73f7e50597d773bab9332874ab5b6f1ea5 frfi <<EMAIL>> 1747615095 +0200	commit: Add new neo4j cloud instance
22e8ba73f7e50597d773bab9332874ab5b6f1ea5 b6d6993791c7a7979a35ed610882d37d2d30de04 frfi <<EMAIL>> 1747620120 +0200	commit: Refactor entity handling in Neo4j service and fuzzy retriever; update types and improve query results
b6d6993791c7a7979a35ed610882d37d2d30de04 cfa9607c74235298e3252823a3bfaf84a15d4000 frfi <<EMAIL>> 1747784058 +0200	commit: Add entity filtering prompt and enhance extraction process with knowledge graph connections
cfa9607c74235298e3252823a3bfaf84a15d4000 d353b392ae8aafed07029f87cabd52ecd798ba95 frfi <<EMAIL>> 1747786323 +0200	commit: Add support for HuggingFace models in LLM service; update settings and initialization methods
d353b392ae8aafed07029f87cabd52ecd798ba95 b2495e7aa2e8a2eb985194068bc2ed24134d32c9 frfi <<EMAIL>> 1747787471 +0200	commit: Enhance claim verification UI with organized tabs for results, extracted facts, and knowledge retrieval details
b2495e7aa2e8a2eb985194068bc2ed24134d32c9 0a2a4ddd30e6a00ba6f0ec10ce3c0b2b3bd3ac2a frfi <<EMAIL>> 1749386144 +0200	commit: Add score attribute to Neo4JEntity; update entity retrieval and connection handling.
0a2a4ddd30e6a00ba6f0ec10ce3c0b2b3bd3ac2a 7a50f455a5ff5084a155e2cdded927c32014df22 frfi <<EMAIL>> 1749397224 +0200	commit: WIP: Without connections
7a50f455a5ff5084a155e2cdded927c32014df22 7b8d38957a61d5188826cbcc70f5dd18c99e77e2 frfi <<EMAIL>> 1749398098 +0200	commit (amend): Remove connection steps and simplify pipeline
7b8d38957a61d5188826cbcc70f5dd18c99e77e2 eacd0cf2cab8406c45ea19c44df3513f854e4f02 frfi <<EMAIL>> 1749456259 +0200	commit: Add max_relation_matches configuration and enhance entity-relation handling, search for entities and relations if no relation was found
eacd0cf2cab8406c45ea19c44df3513f854e4f02 2717121eca5855d99730b31401ffc114e3f91c1e frfi <<EMAIL>> 1749532831 +0200	commit: Implement COT and few-shot Prompt for extractor, extend pipeline to load testdata from yaml file
2717121eca5855d99730b31401ffc114e3f91c1e 0c963a78f8f63111474934f606470aeebd1453f1 frfi <<EMAIL>> 1749534361 +0200	commit: Add metadata for knowledge retrieval
0c963a78f8f63111474934f606470aeebd1453f1 95501438cb71b87483b44a9053cfca22d16db1c7 frfi <<EMAIL>> 1749536679 +0200	commit: WIP: Updated UI
95501438cb71b87483b44a9053cfca22d16db1c7 357d81849970b1c23c7dc0c74bbc72e24ce95516 frfi <<EMAIL>> 1749537200 +0200	commit (amend): Update UI to use new multy-strategy pipeline
357d81849970b1c23c7dc0c74bbc72e24ce95516 694d54ddc8eb4aefdcde3e641942e886af08ac2b frfi <<EMAIL>> 1749794899 +0200	commit: Rename testdata.yaml to testdata_v1.yaml and update references; add testdata_v2.yaml with new test cases
694d54ddc8eb4aefdcde3e641942e886af08ac2b 4d74047388b56e1c26c85b44f7ec872be05a8981 frfi <<EMAIL>> 1749794942 +0200	commit: Add testresults
